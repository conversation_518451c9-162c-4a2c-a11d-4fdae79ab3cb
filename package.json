{"name": "iam-backend", "version": "1.0.0", "description": "Production-ready IAM backend with role-based access control", "type": "module", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "prepare:db": "npx prisma generate && npx prisma migrate deploy && npx prisma db seed", "test:watch": "jest --watch", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:deploy": "prisma migrate deploy", "db:seed": "node prisma/seed.js", "db:studio": "prisma studio", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["iam", "authentication", "authorization", "rbac", "express", "nodejs"], "author": "", "license": "MIT", "dependencies": {"@prisma/client": "^6.13.0", "axios": "^1.11.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-async-errors": "^3.1.1", "express-rate-limit": "^7.1.5", "express-slow-down": "^2.1.0", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "winston": "^3.11.0"}, "devDependencies": {"eslint": "^8.56.0", "eslint-config-node": "^4.1.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prisma": "^6.13.0", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}, "prisma": {"seed": "node prisma/seed.js"}}